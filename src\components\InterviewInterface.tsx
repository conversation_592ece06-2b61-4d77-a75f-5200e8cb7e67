'use client';

import { useChat } from 'ai/react';
import { useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>cO<PERSON>, <PERSON> } from 'lucide-react';
import { useInterviewState } from '@/hooks/useInterviewState';
import { useAudio } from '@/hooks/useAudio';
import { InterviewService } from '@/services/interviewService';
import { InterviewState, MessageRole } from '@/types/interview';
import { UI_CONFIG, STATUS_MESSAGES } from '@/constants/app';
import { ErrorHandler } from '@/utils/errorHandler';

export default function InterviewInterface() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;
  
  const {
    state,
    isListening,
    hasStarted,
    currentTranscript,
    setState,
    setIsListening,
    setHasStarted,
    setCurrentTranscript,
    resetTranscript,
    getStatusText,
    isMicrophoneDisabled,
    getMicrophoneButtonVariant,
  } = useInterviewState();

  const { messages, input, setInput, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    initialMessages: [InterviewService.getInitialMessage()],
  });

  // Audio event handlers
  const handleTranscriptUpdate = useCallback((transcript: string, isFinal: boolean) => {
    InterviewService.processTranscriptUpdate(
      transcript,
      isFinal,
      currentTranscript,
      setInput,
      setCurrentTranscript
    );
  }, [currentTranscript, setInput, setCurrentTranscript]);

  const handleUtteranceEnd = useCallback(() => {
    console.log('Utterance ended');
  }, []);

  const handleAudioError = useCallback((error: any) => {
    const audioError = ErrorHandler.handleAudioError(error);
    console.error('Audio error:', audioError.message);
    setState(InterviewState.READY);
  }, [setState]);

  const handleSpeakingStart = useCallback(() => {
    setState(InterviewState.SPEAKING);
  }, [setState]);

  const handleSpeakingEnd = useCallback(() => {
    setState(InterviewState.READY);
  }, [setState]);

  const handleListeningStart = useCallback(() => {
    resetTranscript();
    setInput(STATUS_MESSAGES.LISTENING);
    setIsListening(true);
  }, [resetTranscript, setInput, setIsListening]);

  const handleListeningEnd = useCallback(() => {
    setIsListening(false);
  }, [setIsListening]);

  const {
    startListening,
    stopListening,
    speakText,
    cleanup,
  } = useAudio({
    onTranscriptUpdate: handleTranscriptUpdate,
    onUtteranceEnd: handleUtteranceEnd,
    onError: handleAudioError,
    onSpeakingStart: handleSpeakingStart,
    onSpeakingEnd: handleSpeakingEnd,
    onListeningStart: handleListeningStart,
    onListeningEnd: handleListeningEnd,
    onStateChange: setState,
  });

  // Handle microphone button click
  const handleMicrophoneClick = useCallback(() => {
    if (isListening) {
      stopListening();
      InterviewService.handleTranscriptSubmission(
        currentTranscript,
        setInput,
        handleSubmit,
        () => setState(InterviewState.READY),
        UI_CONFIG.TIMEOUTS.TRANSCRIPT_SUBMIT_DELAY
      );
    } else {
      startListening();
    }
  }, [isListening, stopListening, startListening, currentTranscript, setInput, handleSubmit, setState]);

  // Start interview
  const startInterview = useCallback(() => {
    setHasStarted(true);
    const initialMessage = messages[0];
    if (initialMessage && initialMessage.role === MessageRole.ASSISTANT) {
      speakText(initialMessage.content);
    }
  }, [setHasStarted, messages, speakText]);

  // End interview
  const endInterview = useCallback(async () => {
    try {
      await InterviewService.endSession(sessionId, messages);
      router.push(`/history/${sessionId}`);
    } catch (error) {
      const appError = ErrorHandler.handle(error, 'InterviewInterface.endInterview');
      console.error('Failed to end interview:', appError.message);
      alert(ErrorHandler.getUserMessage(error));
    }
  }, [sessionId, messages, router]);

  // Handle new assistant messages
  useEffect(() => {
    if (!hasStarted || isLoading) return;
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === MessageRole.ASSISTANT && messages.length > 1) {
      speakText(lastMessage.content);
    }
  }, [messages, isLoading, hasStarted, speakText]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Interview Session</span>
              <div className="text-sm font-normal text-muted-foreground">
                Status: {getStatusText()}
              </div>
            </CardTitle>
          </CardHeader>
        </Card>

        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Conversation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`space-y-4 ${UI_CONFIG.MESSAGES.MAX_HEIGHT} overflow-y-auto`}>
              {messages.map((message) => (
                <div key={message.id} className="p-3 rounded-lg bg-gray-100">
                  <div className="font-semibold mb-1">
                    {message.role === MessageRole.ASSISTANT ? 'Interviewer' : 'You'}:
                  </div>
                  <div>{message.content}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-end mb-4">
              <Button variant="destructive" onClick={endInterview}>
                <Square className="w-4 h-4 mr-2" />
                End Interview
              </Button>
            </div>
            
            <div className="flex justify-center">
              {!hasStarted ? (
                <Button
                  size="lg"
                  onClick={startInterview}
                  className="h-12 px-6"
                >
                  Start Interview
                </Button>
              ) : (
                <Button
                  size="lg"
                  variant={getMicrophoneButtonVariant()}
                  onClick={handleMicrophoneClick}
                  disabled={isMicrophoneDisabled()}
                  className="h-16 w-16 rounded-full"
                >
                  {isListening ? (
                    <MicOff className="w-6 h-6" />
                  ) : (
                    <Mic className="w-6 h-6" />
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
