import { useEffect, useCallback, useRef } from 'react';
import { AudioService } from '@/services/audioService';
import { InterviewService } from '@/services/interviewService';
import { InterviewState } from '@/types/interview';
import { ERROR_MESSAGES, UI_CONFIG } from '@/constants/app';

export interface UseAudioReturn {
  initializeAudio: () => Promise<void>;
  startListening: () => Promise<void>;
  stopListening: () => void;
  speakText: (text: string) => Promise<void>;
  cleanup: () => void;
}

export interface UseAudioProps {
  onTranscriptUpdate: (transcript: string, isFinal: boolean) => void;
  onUtteranceEnd: () => void;
  onError: (error: any) => void;
  onSpeakingStart: () => void;
  onSpeakingEnd: () => void;
  onListeningStart: () => void;
  onListeningEnd: () => void;
  onStateChange: (state: InterviewState) => void;
}

/**
 * Custom hook for managing audio functionality
 */
export function useAudio({
  onTranscriptUpdate,
  onUtteranceEnd,
  onError,
  onSpeakingStart,
  onSpeakingEnd,
  onListeningStart,
  onListeningEnd,
  onStateChange,
}: UseAudioProps): UseAudioReturn {
  const isInitializedRef = useRef(false);

  const initializeAudio = useCallback(async () => {
    if (isInitializedRef.current) return;

    try {
      await AudioService.initializeDeepgram(
        onTranscriptUpdate,
        onUtteranceEnd,
        onError
      );
      isInitializedRef.current = true;
      console.log('Audio initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      onError(error);
    }
  }, [onTranscriptUpdate, onUtteranceEnd, onError]);

  const startListening = useCallback(async () => {
    try {
      // Ensure audio is initialized
      if (!AudioService.isConnectionReady()) {
        console.log('Reinitializing audio connection...');
        try {
          await initializeAudio();
        } catch (error) {
          console.error('Failed to initialize audio connection:', error);
          alert(ERROR_MESSAGES.DEEPGRAM_CONNECTION_FAILED);
          onStateChange(InterviewState.READY);
          return;
        }
      }

      // Start recording
      await AudioService.startRecording();
      onListeningStart();
      onStateChange(InterviewState.LISTENING);
      
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      onStateChange(InterviewState.READY);
      onError(error);
    }
  }, [initializeAudio, onListeningStart, onStateChange, onError]);

  const stopListening = useCallback(() => {
    onListeningEnd();
    onStateChange(InterviewState.THINKING);
    AudioService.stopRecording();
  }, [onListeningEnd, onStateChange]);

  const speakText = useCallback(async (text: string) => {
    try {
      await InterviewService.speakText(
        text,
        onSpeakingStart,
        onSpeakingEnd
      );
    } catch (error) {
      console.error('Error with TTS:', error);
      onSpeakingEnd();
      onError(error);
    }
  }, [onSpeakingStart, onSpeakingEnd, onError]);

  const cleanup = useCallback(() => {
    AudioService.cleanup();
    isInitializedRef.current = false;
  }, []);

  // Initialize audio on mount
  useEffect(() => {
    initializeAudio().catch(console.error);

    return () => {
      cleanup();
    };
  }, [initializeAudio, cleanup]);

  return {
    initializeAudio,
    startListening,
    stopListening,
    speakText,
    cleanup,
  };
}
