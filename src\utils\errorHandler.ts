import { ERROR_MESSAGES } from '@/constants/app';

/**
 * Custom error classes for better error handling
 */
export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
    if (field) {
      this.message = `${field}: ${message}`;
    }
  }
}

export class AudioError extends AppError {
  constructor(message: string, code: string = 'AUDIO_ERROR') {
    super(message, code, 500);
    this.name = 'AudioError';
  }
}

export class SessionError extends AppError {
  constructor(message: string, code: string = 'SESSION_ERROR') {
    super(message, code, 500);
    this.name = 'SessionError';
  }
}

export class NetworkError extends AppError {
  constructor(message: string, statusCode: number = 500) {
    super(message, 'NETWORK_ERROR', statusCode);
    this.name = 'NetworkError';
  }
}

/**
 * Error handler utility class
 */
export class ErrorHandler {
  /**
   * Handles and logs errors consistently
   */
  static handle(error: unknown, context?: string): AppError {
    const contextPrefix = context ? `[${context}] ` : '';
    
    if (error instanceof AppError) {
      console.error(`${contextPrefix}${error.name}:`, error.message, error.code);
      return error;
    }

    if (error instanceof Error) {
      console.error(`${contextPrefix}Error:`, error.message);
      return new AppError(error.message, 'UNKNOWN_ERROR', 500);
    }

    const message = typeof error === 'string' ? error : 'An unknown error occurred';
    console.error(`${contextPrefix}Unknown error:`, message);
    return new AppError(message, 'UNKNOWN_ERROR', 500);
  }

  /**
   * Creates a user-friendly error message
   */
  static getUserMessage(error: unknown): string {
    if (error instanceof AppError) {
      return error.message;
    }

    if (error instanceof Error) {
      // Map common error types to user-friendly messages
      if (error.message.includes('fetch')) {
        return 'Network connection error. Please check your internet connection.';
      }
      if (error.message.includes('permission')) {
        return 'Permission denied. Please check your browser settings.';
      }
      if (error.message.includes('timeout')) {
        return 'Request timed out. Please try again.';
      }
      return error.message;
    }

    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Handles API errors specifically
   */
  static handleApiError(response: Response, context?: string): AppError {
    const message = `API Error: ${response.status} ${response.statusText}`;
    const contextPrefix = context ? `[${context}] ` : '';
    
    console.error(`${contextPrefix}${message}`);
    
    switch (response.status) {
      case 400:
        return new ValidationError('Invalid request data');
      case 401:
        return new AppError('Unauthorized access', 'UNAUTHORIZED', 401);
      case 403:
        return new AppError('Access forbidden', 'FORBIDDEN', 403);
      case 404:
        return new AppError('Resource not found', 'NOT_FOUND', 404);
      case 429:
        return new AppError('Too many requests', 'RATE_LIMITED', 429);
      case 500:
        return new AppError('Internal server error', 'SERVER_ERROR', 500);
      default:
        return new NetworkError(message, response.status);
    }
  }

  /**
   * Handles audio-related errors
   */
  static handleAudioError(error: unknown): AudioError {
    if (error instanceof AudioError) {
      return error;
    }

    const message = error instanceof Error ? error.message : String(error);
    
    if (message.includes('permission')) {
      return new AudioError(ERROR_MESSAGES.DEEPGRAM_CONNECTION_FAILED, 'PERMISSION_DENIED');
    }
    if (message.includes('timeout')) {
      return new AudioError(ERROR_MESSAGES.CONNECTION_TIMEOUT, 'CONNECTION_TIMEOUT');
    }
    if (message.includes('API key')) {
      return new AudioError(ERROR_MESSAGES.API_KEY_NOT_CONFIGURED, 'API_KEY_MISSING');
    }
    
    return new AudioError(message, 'AUDIO_PROCESSING_ERROR');
  }

  /**
   * Handles session-related errors
   */
  static handleSessionError(error: unknown): SessionError {
    if (error instanceof SessionError) {
      return error;
    }

    const message = error instanceof Error ? error.message : String(error);
    
    if (message.includes('not found')) {
      return new SessionError(ERROR_MESSAGES.SESSION_NOT_FOUND, 'SESSION_NOT_FOUND');
    }
    if (message.includes('create')) {
      return new SessionError(ERROR_MESSAGES.SESSION_CREATE_FAILED, 'SESSION_CREATE_FAILED');
    }
    if (message.includes('update')) {
      return new SessionError(ERROR_MESSAGES.SESSION_UPDATE_FAILED, 'SESSION_UPDATE_FAILED');
    }
    
    return new SessionError(message, 'SESSION_ERROR');
  }

  /**
   * Logs error with additional context
   */
  static logError(error: unknown, context: Record<string, any> = {}): void {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      timestamp,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
    };

    console.error('Error logged:', JSON.stringify(errorInfo, null, 2));
  }

  /**
   * Determines if an error should be retried
   */
  static shouldRetry(error: unknown): boolean {
    if (error instanceof AppError) {
      return error.code === 'NETWORK_ERROR' || 
             error.code === 'CONNECTION_TIMEOUT' ||
             error.statusCode >= 500;
    }

    if (error instanceof Error) {
      return error.message.includes('timeout') ||
             error.message.includes('network') ||
             error.message.includes('fetch');
    }

    return false;
  }

  /**
   * Creates a retry wrapper for async functions
   */
  static async withRetry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries || !this.shouldRetry(error)) {
          throw error;
        }

        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    throw lastError;
  }
}
