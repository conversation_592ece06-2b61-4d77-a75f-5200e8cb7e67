// Enums for better type safety and maintainability
export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export enum SessionStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
}

export enum InterviewState {
  READY = 'ready',
  LISTENING = 'listening',
  THINKING = 'thinking',
  SPEAKING = 'speaking',
}

// Core interfaces
export interface Message {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: Date;
}

export interface InterviewSession {
  _id?: string;
  sessionId: string;
  messages: Message[];
  startTime: Date;
  endTime?: Date;
  status: SessionStatus;
  audioRecorded: boolean;
}

// Audio-related interfaces
export interface AudioConfig {
  model: string;
  language: string;
  smartFormat: boolean;
  interimResults: boolean;
  utteranceEndMs: number;
  vadEvents: boolean;
}

export interface TTSConfig {
  model: string;
}

// API Response interfaces
export interface SessionResponse {
  sessionId: string;
}

export interface SessionsResponse {
  sessions: InterviewSession[];
}

export interface SessionDetailResponse {
  session: InterviewSession;
}
