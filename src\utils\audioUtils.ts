/**
 * Utility functions for audio processing and management
 */
export class AudioUtils {
  /**
   * Checks if the browser supports audio recording
   */
  static isAudioRecordingSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  /**
   * Checks if the browser supports Web Audio API
   */
  static isWebAudioSupported(): boolean {
    return !!(window.AudioContext || (window as any).webkitAudioContext);
  }

  /**
   * Gets available audio input devices
   */
  static async getAudioInputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('Error getting audio input devices:', error);
      return [];
    }
  }

  /**
   * Gets available audio output devices
   */
  static async getAudioOutputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audiooutput');
    } catch (error) {
      console.error('Error getting audio output devices:', error);
      return [];
    }
  }

  /**
   * Requests microphone permission
   */
  static async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // Stop the stream immediately as we only needed permission
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      return false;
    }
  }

  /**
   * Checks if microphone permission is granted
   */
  static async checkMicrophonePermission(): Promise<PermissionState | null> {
    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        return permission.state;
      }
      return null;
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return null;
    }
  }

  /**
   * Creates an audio context with proper browser compatibility
   */
  static createAudioContext(): AudioContext | null {
    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      return new AudioContextClass();
    } catch (error) {
      console.error('Error creating audio context:', error);
      return null;
    }
  }

  /**
   * Converts audio blob to base64
   */
  static async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]); // Remove data:audio/wav;base64, prefix
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Converts base64 to audio blob
   */
  static base64ToBlob(base64: string, mimeType: string = 'audio/wav'): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Gets audio duration from blob
   */
  static async getAudioDuration(blob: Blob): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      const url = URL.createObjectURL(blob);
      
      audio.onloadedmetadata = () => {
        URL.revokeObjectURL(url);
        resolve(audio.duration);
      };
      
      audio.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load audio'));
      };
      
      audio.src = url;
    });
  }

  /**
   * Validates audio file type
   */
  static isValidAudioType(file: File): boolean {
    const validTypes = [
      'audio/wav',
      'audio/mp3',
      'audio/mpeg',
      'audio/webm',
      'audio/ogg',
      'audio/m4a'
    ];
    return validTypes.includes(file.type);
  }

  /**
   * Checks if audio file size is within limits
   */
  static isValidAudioSize(file: File, maxSizeMB: number = 10): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }

  /**
   * Creates a silent audio buffer
   */
  static createSilentBuffer(audioContext: AudioContext, duration: number): AudioBuffer {
    const sampleRate = audioContext.sampleRate;
    const frameCount = sampleRate * duration;
    const buffer = audioContext.createBuffer(1, frameCount, sampleRate);
    
    // Fill with silence (zeros)
    const channelData = buffer.getChannelData(0);
    for (let i = 0; i < frameCount; i++) {
      channelData[i] = 0;
    }
    
    return buffer;
  }

  /**
   * Normalizes audio volume
   */
  static normalizeAudioVolume(audioData: Float32Array): Float32Array {
    let max = 0;
    
    // Find the maximum absolute value
    for (let i = 0; i < audioData.length; i++) {
      const abs = Math.abs(audioData[i]);
      if (abs > max) {
        max = abs;
      }
    }
    
    // Normalize if max is greater than 0
    if (max > 0) {
      const normalized = new Float32Array(audioData.length);
      for (let i = 0; i < audioData.length; i++) {
        normalized[i] = audioData[i] / max;
      }
      return normalized;
    }
    
    return audioData;
  }
}
