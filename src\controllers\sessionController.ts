import { NextRequest, NextResponse } from 'next/server';
import { SessionService } from '@/services/sessionService';
import { SessionResponse, SessionsResponse, SessionDetailResponse } from '@/types/interview';
import { HTTP_STATUS, ERROR_MESSAGES } from '@/constants/app';

export class Session<PERSON>ontroller {
  /**
   * Creates a new interview session
   */
  static async createSession(): Promise<NextResponse<SessionResponse | { error: string }>> {
    try {
      const sessionId = await SessionService.createSession();
      return NextResponse.json({ sessionId });
    } catch (error) {
      console.error('Session creation error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.SESSION_CREATE_FAILED },
        { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      );
    }
  }

  /**
   * Retrieves all sessions
   */
  static async getAllSessions(): Promise<NextResponse<SessionsResponse | { error: string }>> {
    try {
      const sessions = await SessionService.getAllSessions();
      return NextResponse.json({ sessions });
    } catch (error) {
      console.error('Sessions fetch error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.SESSIONS_FETCH_FAILED },
        { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      );
    }
  }

  /**
   * Retrieves a specific session by ID
   */
  static async getSessionById(
    sessionId: string
  ): Promise<NextResponse<SessionDetailResponse | { error: string }>> {
    try {
      const session = await SessionService.getSessionById(sessionId);
      
      if (!session) {
        return NextResponse.json(
          { error: ERROR_MESSAGES.SESSION_NOT_FOUND },
          { status: HTTP_STATUS.NOT_FOUND }
        );
      }
      
      return NextResponse.json({ session });
    } catch (error) {
      console.error('Session fetch error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.SESSION_FETCH_FAILED },
        { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      );
    }
  }

  /**
   * Updates a session with new data
   */
  static async updateSession(
    sessionId: string,
    req: NextRequest
  ): Promise<NextResponse<{ success: boolean } | { error: string }>> {
    try {
      const updates = await req.json();
      const success = await SessionService.updateSession(sessionId, updates);
      
      if (!success) {
        return NextResponse.json(
          { error: ERROR_MESSAGES.SESSION_NOT_FOUND },
          { status: HTTP_STATUS.NOT_FOUND }
        );
      }
      
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Session update error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.SESSION_UPDATE_FAILED },
        { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      );
    }
  }

  /**
   * Handles session-related errors consistently
   */
  static handleError(error: unknown, defaultMessage: string): NextResponse {
    const message = error instanceof Error ? error.message : defaultMessage;
    console.error('Session controller error:', error);
    
    return NextResponse.json(
      { error: message },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    );
  }
}
