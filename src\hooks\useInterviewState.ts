import { useState, useCallback } from 'react';
import { InterviewState } from '@/types/interview';
import { InterviewService } from '@/services/interviewService';

export interface UseInterviewStateReturn {
  state: InterviewState;
  isListening: boolean;
  hasStarted: boolean;
  currentTranscript: string;
  setState: (state: InterviewState) => void;
  setIsListening: (listening: boolean) => void;
  setHasStarted: (started: boolean) => void;
  setCurrentTranscript: React.Dispatch<React.SetStateAction<string>>;
  resetTranscript: () => void;
  getStatusText: () => string;
  canUserInteract: () => boolean;
  isMicrophoneDisabled: () => boolean;
  getMicrophoneButtonVariant: () => 'default' | 'destructive';
}

/**
 * Custom hook for managing interview state
 */
export function useInterviewState(): UseInterviewStateReturn {
  const [state, setState] = useState<InterviewState>(InterviewState.READY);
  const [isListening, setIsListening] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  const resetTranscript = useCallback(() => {
    setCurrentTranscript('');
  }, []);

  const getStatusText = useCallback(() => {
    return InterviewService.getStatusText(state);
  }, [state]);

  const canUserInteract = useCallback(() => {
    return InterviewService.canUserInteract(state);
  }, [state]);

  const isMicrophoneDisabled = useCallback(() => {
    return InterviewService.isMicrophoneDisabled(state);
  }, [state]);

  const getMicrophoneButtonVariant = useCallback(() => {
    return InterviewService.getMicrophoneButtonVariant(isListening);
  }, [isListening]);

  return {
    state,
    isListening,
    hasStarted,
    currentTranscript,
    setState,
    setIsListening,
    setHasStarted,
    setCurrentTranscript,
    resetTranscript,
    getStatusText,
    canUserInteract,
    isMicrophoneDisabled,
    getMicrophoneButtonVariant,
  };
}
