import { createClient } from '@deepgram/sdk';
import { NextRequest, NextResponse } from 'next/server';
import { AUDIO_CONFIG, ERROR_MESSAGES, ENV_KEYS, HTTP_STATUS } from '@/constants/app';

export class TTSController {
  /**
   * Generates speech from text using Deepgram TTS
   */
  static async generateSpeech(req: NextRequest): Promise<NextResponse> {
    try {
      const { text } = await req.json();
      
      if (!text || typeof text !== 'string') {
        return NextResponse.json(
          { error: 'Text is required' },
          { status: HTTP_STATUS.BAD_REQUEST }
        );
      }

      const apiKey = process.env[ENV_KEYS.DEEPGRAM_API_KEY];
      if (!apiKey) {
        return NextResponse.json(
          { error: ERROR_MESSAGES.DEEPGRAM_API_KEY_MISSING },
          { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
        );
      }

      const deepgram = createClient(apiKey);
      
      const response = await deepgram.speak.request(
        { text },
        { model: AUDIO_CONFIG.TTS.MODEL }
      );

      const stream = await response.getStream();
      if (!stream) {
        return NextResponse.json(
          { error: ERROR_MESSAGES.AUDIO_STREAM_FAILED },
          { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
        );
      }

      const audioBuffer = await this.streamToBuffer(stream);
      
      return new NextResponse(audioBuffer, {
        headers: {
          'Content-Type': 'audio/wav',
        },
      });
    } catch (error) {
      console.error('TTS Error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.TTS_GENERATION_FAILED },
        { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      );
    }
  }

  /**
   * Converts a readable stream to a buffer
   */
  private static async streamToBuffer(stream: ReadableStream): Promise<Uint8Array> {
    const reader = stream.getReader();
    const chunks: Uint8Array[] = [];
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }
    
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
    const audioBuffer = new Uint8Array(totalLength);
    
    let offset = 0;
    for (const chunk of chunks) {
      audioBuffer.set(chunk, offset);
      offset += chunk.length;
    }
    
    return audioBuffer;
  }

  /**
   * Validates TTS request
   */
  static validateTTSRequest(body: any): { isValid: boolean; error?: string } {
    if (!body) {
      return { isValid: false, error: 'Request body is required' };
    }

    if (!body.text) {
      return { isValid: false, error: 'Text is required' };
    }

    if (typeof body.text !== 'string') {
      return { isValid: false, error: 'Text must be a string' };
    }

    if (body.text.trim().length === 0) {
      return { isValid: false, error: 'Text cannot be empty' };
    }

    if (body.text.length > 5000) {
      return { isValid: false, error: 'Text is too long (max 5000 characters)' };
    }

    return { isValid: true };
  }

  /**
   * Sanitizes text for TTS processing
   */
  static sanitizeText(text: string): string {
    return text
      .trim()
      .replace(/[^\w\s.,!?;:'"()-]/g, '') // Remove special characters
      .replace(/\s+/g, ' '); // Normalize whitespace
  }
}
