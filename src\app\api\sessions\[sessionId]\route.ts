import { NextRequest } from 'next/server';
import { SessionController } from '@/controllers/sessionController';

export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  return SessionController.getSessionById(params.sessionId);
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  return SessionController.updateSession(params.sessionId, req);
}
