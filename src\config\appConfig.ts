import { AUDIO_CONFIG, UI_CONFIG, API_ENDPOINTS } from '@/constants/app';
import { AudioConfig, TTSConfig } from '@/types/interview';

/**
 * Application configuration management
 */
export class AppConfig {
  private static instance: AppConfig;
  private audioConfig: AudioConfig;
  private ttsConfig: TTSConfig;

  private constructor() {
    this.audioConfig = { ...AUDIO_CONFIG.DEEPGRAM };
    this.ttsConfig = { ...AUDIO_CONFIG.TTS };
  }

  /**
   * Gets the singleton instance
   */
  static getInstance(): AppConfig {
    if (!AppConfig.instance) {
      AppConfig.instance = new AppConfig();
    }
    return AppConfig.instance;
  }

  /**
   * Gets audio configuration
   */
  getAudioConfig(): AudioConfig {
    return { ...this.audioConfig };
  }

  /**
   * Gets TTS configuration
   */
  getTTSConfig(): TTSConfig {
    return { ...this.ttsConfig };
  }

  /**
   * Updates audio configuration
   */
  updateAudioConfig(config: Partial<AudioConfig>): void {
    this.audioConfig = { ...this.audioConfig, ...config };
  }

  /**
   * Updates TTS configuration
   */
  updateTTSConfig(config: Partial<TTSConfig>): void {
    this.ttsConfig = { ...this.ttsConfig, ...config };
  }

  /**
   * Gets API endpoints
   */
  getApiEndpoints() {
    return { ...API_ENDPOINTS };
  }

  /**
   * Gets UI configuration
   */
  getUIConfig() {
    return { ...UI_CONFIG };
  }

  /**
   * Gets recording configuration
   */
  getRecordingConfig() {
    return { ...AUDIO_CONFIG.RECORDING };
  }

  /**
   * Gets timeout configurations
   */
  getTimeouts() {
    return { ...UI_CONFIG.TIMEOUTS };
  }

  /**
   * Validates audio configuration
   */
  validateAudioConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.audioConfig.model) {
      errors.push('Audio model is required');
    }

    if (!this.audioConfig.language) {
      errors.push('Audio language is required');
    }

    if (this.audioConfig.utteranceEndMs < 100 || this.audioConfig.utteranceEndMs > 5000) {
      errors.push('Utterance end time must be between 100ms and 5000ms');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validates TTS configuration
   */
  validateTTSConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.ttsConfig.model) {
      errors.push('TTS model is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Resets configuration to defaults
   */
  resetToDefaults(): void {
    this.audioConfig = { ...AUDIO_CONFIG.DEEPGRAM };
    this.ttsConfig = { ...AUDIO_CONFIG.TTS };
  }

  /**
   * Gets configuration summary for debugging
   */
  getConfigSummary(): Record<string, any> {
    return {
      audio: this.audioConfig,
      tts: this.ttsConfig,
      ui: UI_CONFIG,
      api: API_ENDPOINTS,
    };
  }

  /**
   * Checks if configuration is valid
   */
  isValid(): boolean {
    const audioValidation = this.validateAudioConfig();
    const ttsValidation = this.validateTTSConfig();
    
    return audioValidation.isValid && ttsValidation.isValid;
  }

  /**
   * Gets all validation errors
   */
  getValidationErrors(): string[] {
    const audioValidation = this.validateAudioConfig();
    const ttsValidation = this.validateTTSConfig();
    
    return [...audioValidation.errors, ...ttsValidation.errors];
  }
}

// Export singleton instance
export const appConfig = AppConfig.getInstance();
