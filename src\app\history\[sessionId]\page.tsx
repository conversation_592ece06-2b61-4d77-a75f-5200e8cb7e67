'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock, Play } from 'lucide-react';
import { InterviewSession } from '@/types/interview';

export default function HistoryDetailPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;
  
  const [session, setSession] = useState<InterviewSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const response = await fetch(`/api/sessions/${sessionId}`);
        const { session } = await response.json();
        setSession(session);
      } catch (error) {
        console.error('Failed to fetch session:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSession();
  }, [sessionId]);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const calculateDuration = (start: string, end?: string) => {
    if (!end) return 'Session ongoing';
    const duration = new Date(end).getTime() - new Date(start).getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 flex items-center justify-center">
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">Session not found</p>
            <Button onClick={() => router.push('/history')}>
              Back to History
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/history')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to History
          </Button>
          <h1 className="text-2xl font-bold">Interview Details</h1>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Interview Session</CardTitle>
            <CardDescription className="flex flex-col space-y-2">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                {formatDate(session.startTime.toString())} at {formatTime(session.startTime.toString())}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                Duration: {calculateDuration(
                  session.startTime.toString(),
                  session.endTime?.toString()
                )}
              </div>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 rounded-full text-sm ${
                  session.status === 'completed'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {session.status === 'completed' ? 'Completed' : 'Active'}
                </span>
                <span className="text-sm text-muted-foreground">
                  {session.messages.length} messages
                </span>
              </div>
              {session.audioRecorded && (
                <Button variant="outline" size="sm">
                  <Play className="w-4 h-4 mr-2" />
                  Play Full Recording
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Interview Transcript</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {session.messages.map((message, index) => (
                <div key={message.id || index} className="border-l-4 border-gray-200 pl-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-semibold text-lg">
                      {message.role === 'assistant' ? 'Interviewer' : 'You'}:
                    </div>
                    {session.audioRecorded && message.role === 'user' && (
                      <Button variant="ghost" size="sm">
                        <Play className="w-4 h-4 mr-1" />
                        Play
                      </Button>
                    )}
                  </div>
                  <div className="text-gray-700 leading-relaxed">
                    {message.content}
                  </div>
                  <div className="text-xs text-muted-foreground mt-2">
                    {message.timestamp && formatTime(message.timestamp.toString())}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}