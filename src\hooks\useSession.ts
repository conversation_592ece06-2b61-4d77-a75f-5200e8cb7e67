import { useState, useEffect, useCallback } from 'react';
import { InterviewSession } from '@/types/interview';
import { SessionService } from '@/services/sessionService';

export interface UseSessionReturn {
  sessions: InterviewSession[];
  loading: boolean;
  error: string | null;
  fetchSessions: () => Promise<void>;
  createSession: () => Promise<string>;
  getSession: (sessionId: string) => Promise<InterviewSession | null>;
  updateSession: (sessionId: string, updates: Partial<InterviewSession>) => Promise<boolean>;
  endSession: (sessionId: string, messages: any[]) => Promise<boolean>;
  formatDate: (date: Date) => string;
  formatTime: (date: Date) => string;
  calculateDuration: (startTime: Date, endTime?: Date) => string;
}

/**
 * Custom hook for managing session operations
 */
export function useSession(): UseSessionReturn {
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSessions = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const fetchedSessions = await SessionService.getAllSessions();
      setSessions(fetchedSessions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sessions';
      setError(errorMessage);
      console.error('Failed to fetch sessions:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createSession = useCallback(async (): Promise<string> => {
    try {
      setError(null);
      return await SessionService.createSession();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
      setError(errorMessage);
      console.error('Failed to create session:', err);
      throw err;
    }
  }, []);

  const getSession = useCallback(async (sessionId: string): Promise<InterviewSession | null> => {
    try {
      setError(null);
      return await SessionService.getSessionById(sessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get session';
      setError(errorMessage);
      console.error('Failed to get session:', err);
      return null;
    }
  }, []);

  const updateSession = useCallback(async (
    sessionId: string, 
    updates: Partial<InterviewSession>
  ): Promise<boolean> => {
    try {
      setError(null);
      return await SessionService.updateSession(sessionId, updates);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update session';
      setError(errorMessage);
      console.error('Failed to update session:', err);
      return false;
    }
  }, []);

  const endSession = useCallback(async (
    sessionId: string, 
    messages: any[]
  ): Promise<boolean> => {
    try {
      setError(null);
      return await SessionService.endSession(sessionId, messages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to end session';
      setError(errorMessage);
      console.error('Failed to end session:', err);
      return false;
    }
  }, []);

  const formatDate = useCallback((date: Date): string => {
    return SessionService.formatDate(date);
  }, []);

  const formatTime = useCallback((date: Date): string => {
    return SessionService.formatTime(date);
  }, []);

  const calculateDuration = useCallback((startTime: Date, endTime?: Date): string => {
    return SessionService.calculateDuration(startTime, endTime);
  }, []);

  return {
    sessions,
    loading,
    error,
    fetchSessions,
    createSession,
    getSession,
    updateSession,
    endSession,
    formatDate,
    formatTime,
    calculateDuration,
  };
}
