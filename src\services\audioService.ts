import { createClient, LiveTranscriptionEvents } from '@deepgram/sdk';
import { AUDIO_CONFIG, ERROR_MESSAGES, ENV_KEYS } from '@/constants/app';
import { AudioConfig, TTSConfig } from '@/types/interview';

export class AudioService {
  private static deepgramConnection: any = null;
  private static microphone: MediaRecorder | null = null;

  /**
   * Gets the default audio configuration for Deepgram
   */
  static getDefaultAudioConfig(): AudioConfig {
    return {
      model: AUDIO_CONFIG.DEEPGRAM.MODEL,
      language: AUDIO_CONFIG.DEEPGRAM.LANGUAGE,
      smartFormat: AUDIO_CONFIG.DEEPGRAM.SMART_FORMAT,
      interimResults: AUDIO_CONFIG.DEEPGRAM.INTERIM_RESULTS,
      utteranceEndMs: AUDIO_CONFIG.DEEPGRAM.UTTERANCE_END_MS,
      vadEvents: AUDIO_CONFIG.DEEPGRAM.VAD_EVENTS,
    };
  }

  /**
   * Gets the default TTS configuration
   */
  static getDefaultTTSConfig(): TTSConfig {
    return {
      model: AUDIO_CONFIG.TTS.MODEL,
    };
  }

  /**
   * Initializes Deepgram connection for live transcription
   */
  static async initializeDeepgram(
    onTranscript: (transcript: string, isFinal: boolean) => void,
    onUtteranceEnd: () => void,
    onError: (error: any) => void
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Deepgram can only be initialized in browser environment'));
        return;
      }

      const apiKey = process.env[ENV_KEYS.DEEPGRAM_API_KEY_PUBLIC];
      if (!apiKey) {
        reject(new Error(ERROR_MESSAGES.API_KEY_NOT_CONFIGURED));
        return;
      }

      console.log('Initializing Deepgram connection...');
      const deepgram = createClient(apiKey);
      const config = this.getDefaultAudioConfig();
      
      const connection = deepgram.listen.live({
        model: config.model,
        language: config.language,
        smart_format: config.smartFormat,
        interim_results: config.interimResults,
        utterance_end_ms: config.utteranceEndMs,
        vad_events: config.vadEvents,
      });

      connection.on(LiveTranscriptionEvents.Open, () => {
        console.log('Deepgram connection opened');
        this.deepgramConnection = connection;
        resolve(connection);
      });

      connection.on(LiveTranscriptionEvents.Transcript, (data) => {
        const transcript = data.channel?.alternatives?.[0]?.transcript;
        if (transcript) {
          console.log('Deepgram transcript:', transcript, 'is_final:', data.is_final);
          onTranscript(transcript, data.is_final);
        }
      });

      connection.on(LiveTranscriptionEvents.UtteranceEnd, () => {
        console.log('Utterance ended');
        onUtteranceEnd();
      });

      connection.on(LiveTranscriptionEvents.Close, () => {
        console.log('Deepgram connection closed');
        this.deepgramConnection = null;
      });

      connection.on(LiveTranscriptionEvents.Error, (error) => {
        console.error('Deepgram error:', error);
        onError(error);
        reject(error);
      });

      // Set a timeout to reject if connection doesn't open
      setTimeout(() => {
        if (!this.deepgramConnection) {
          reject(new Error(ERROR_MESSAGES.CONNECTION_TIMEOUT));
        }
      }, AUDIO_CONFIG.RECORDING.TIME_SLICE * 50); // 5 seconds
    });
  }

  /**
   * Starts audio recording and streaming to Deepgram
   */
  static async startRecording(): Promise<MediaStream> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      if (!this.deepgramConnection || this.deepgramConnection.getReadyState() !== 1) {
        throw new Error('Deepgram connection not ready');
      }

      const microphone = new MediaRecorder(stream, {
        mimeType: AUDIO_CONFIG.RECORDING.MIME_TYPE,
      });
      
      microphone.ondataavailable = (event) => {
        if (event.data.size > 0 && 
            this.deepgramConnection && 
            this.deepgramConnection.getReadyState() === 1) {
          this.deepgramConnection.send(event.data);
        }
      };
      
      microphone.start(AUDIO_CONFIG.RECORDING.TIME_SLICE);
      this.microphone = microphone;
      
      return stream;
    } catch (error) {
      console.error('Error starting recording:', error);
      throw error;
    }
  }

  /**
   * Stops audio recording
   */
  static stopRecording(): void {
    if (this.microphone) {
      this.microphone.stop();
      this.microphone = null;
    }
  }

  /**
   * Closes Deepgram connection
   */
  static closeConnection(): void {
    if (this.deepgramConnection) {
      this.deepgramConnection.finish();
      this.deepgramConnection = null;
    }
  }

  /**
   * Gets the current connection state
   */
  static getConnectionState(): number | null {
    return this.deepgramConnection ? this.deepgramConnection.getReadyState() : null;
  }

  /**
   * Checks if connection is ready
   */
  static isConnectionReady(): boolean {
    return this.deepgramConnection && this.deepgramConnection.getReadyState() === 1;
  }

  /**
   * Generates speech from text using TTS API
   */
  static async generateSpeech(text: string): Promise<Blob> {
    try {
      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('TTS API error:', errorText);
        throw new Error(ERROR_MESSAGES.TTS_GENERATION_FAILED);
      }
      
      return await response.blob();
    } catch (error) {
      console.error('Error generating speech:', error);
      throw error;
    }
  }

  /**
   * Plays audio from blob with proper configuration
   */
  static async playAudio(audioBlob: Blob, onEnded?: () => void): Promise<void> {
    try {
      const audio = new Audio();
      audio.src = URL.createObjectURL(audioBlob);
      
      // Use headphones/speakers output to minimize echo
      try {
        if ('setSinkId' in audio) {
          await (audio as any).setSinkId('default');
        }
      } catch (e) {
        console.log('Audio output selection not supported');
      }
      
      if (onEnded) {
        audio.onended = onEnded;
      }
      
      // Wait for audio to load before playing to prevent cutoff
      audio.oncanplaythrough = () => {
        audio.play();
      };
      
      // Fallback: play after a short delay if canplaythrough doesn't fire
      setTimeout(() => {
        if (audio.readyState >= 2) { // HAVE_CURRENT_DATA
          audio.play();
        }
      }, AUDIO_CONFIG.RECORDING.TIME_SLICE);
      
    } catch (error) {
      console.error('Error playing audio:', error);
      throw error;
    }
  }

  /**
   * Cleanup method to be called when component unmounts
   */
  static cleanup(): void {
    this.stopRecording();
    this.closeConnection();
  }
}
