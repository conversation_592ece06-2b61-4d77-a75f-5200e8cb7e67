# AI Interview Practice Application

A web application for practicing software engineering interviews with an AI interviewer that provides real-time voice interaction.

## Features

- **Real-time Voice Conversation**: Uses Deepgram for Speech-to-Text (STT) and Text-to-Speech (TTS)
- **Session Management**: Each interview session is tracked and stored
- **Interview History**: Review past interviews with full transcripts
- **Audio Recording**: Optional audio recording during interviews
- **Dynamic Questions**: AI generates follow-up questions based on responses

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **UI**: ShadCN UI components with Tailwind CSS
- **Database**: MongoDB (local instance)
- **AI**: Google Generative AI (Gemini 2.0 Flash) via Vercel AI SDK
- **Voice**: Deepgram SDK for STT and TTS
- **Package Manager**: Bun

## Setup Instructions

### Prerequisites

1. **Node.js**: Version 18+ (or use Bun directly)
2. **MongoDB**: Local MongoDB instance running on port 27017
3. **API Keys**:
   - Google Generative AI API key
   - Deepgram API key

### Installation

1. **Install Dependencies**:
   ```bash
   bun install
   ```

2. **Configure Environment Variables**:
   Update the `.env.local` file with your API keys:
   ```
   GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here
   DEEPGRAM_API_KEY=your_deepgram_api_key_here
   NEXT_PUBLIC_DEEPGRAM_API_KEY=your_deepgram_api_key_here
   MONGODB_URI=mongodb://localhost:27017/ai-interviewer
   ```

3. **Start MongoDB**:
   Make sure MongoDB is running on your local machine:
   ```bash
   mongod
   ```

4. **Run the Development Server**:
   ```bash
   bun dev
   ```

5. **Open the Application**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Usage

1. **Start New Interview**: Click "Start New Interview" on the home page
2. **Voice Interaction**: 
   - Click and hold the microphone button to speak
   - The AI will respond with voice and text
   - Toggle audio recording if desired
3. **End Interview**: Click "End Interview" to save the session
4. **Review History**: Access past interviews from the history page

## Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── chat/              # AI chat endpoint
│   │   └── sessions/          # Session management APIs
│   ├── history/               # Interview history pages
│   ├── interview/             # Active interview page
│   └── page.tsx               # Home page
├── lib/
│   └── mongodb.ts             # MongoDB connection
└── types/
    └── interview.ts           # TypeScript interfaces
```

## API Endpoints

- `POST /api/chat` - Send messages to AI interviewer
- `POST /api/sessions` - Create new interview session
- `GET /api/sessions` - Get all interview sessions
- `GET /api/sessions/[id]` - Get specific session
- `PUT /api/sessions/[id]` - Update session

## Troubleshooting

- **MongoDB Connection Issues**: Ensure MongoDB is running on port 27017
- **Microphone Access**: Grant browser permission for microphone access
- **API Key Issues**: Verify all environment variables are correctly set
- **Build Issues**: Try clearing `.next` folder and rebuilding
