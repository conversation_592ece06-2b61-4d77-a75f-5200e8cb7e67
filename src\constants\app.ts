// Application constants
export const APP_CONFIG = {
  DATABASE_NAME: 'ai-interviewer',
  COLLECTIONS: {
    SESSIONS: 'sessions',
  },
} as const;

// Audio configuration constants
export const AUDIO_CONFIG = {
  DEEPGRAM: {
    MODEL: 'nova-2',
    LANGUAGE: 'en-US',
    SMART_FORMAT: true,
    INTERIM_RESULTS: true,
    UTTERANCE_END_MS: 1000,
    VAD_EVENTS: true,
  },
  TTS: {
    MODEL: 'aura-asteria-en',
  },
  RECORDING: {
    MIME_TYPE: 'audio/webm',
    TIME_SLICE: 100, // Send data every 100ms
  },
} as const;

// API endpoints
export const API_ENDPOINTS = {
  CHAT: '/api/chat',
  SESSIONS: '/api/sessions',
  TTS: '/api/tts',
  STT: '/api/stt',
} as const;

// UI constants
export const UI_CONFIG = {
  MESSAGES: {
    MAX_HEIGHT: 'max-h-96',
  },
  TIMEOUTS: {
    TRANSCRIPT_SUBMIT_DELAY: 1000,
    AUDIO_PLAY_FALLBACK: 100,
    CONNECTION_TIMEOUT: 5000,
  },
  BUTTON_SIZES: {
    LARGE: 'lg',
    DEFAULT: 'default',
  },
} as const;

// System prompts and messages
export const SYSTEM_PROMPTS = {
  INTERVIEWER: `You are a friendly but professional senior software engineer conducting an interview.
             Start the conversation with a brief introduction.
             Ask a mix of technical and behavioral questions.
             Keep your responses concise and conversational.
             Dynamically ask follow-up questions based on the user's answers.
             Do not repeat questions.
             Focus on understanding the candidate's problem-solving approach and technical knowledge.`,
  INITIAL_MESSAGE: "Hello! I'm your AI interviewer today. I'm excited to learn about your background and technical skills. Let's start with a brief introduction - could you tell me about yourself and what brings you to this interview?",
} as const;

// Status messages
export const STATUS_MESSAGES = {
  LISTENING: 'Listening...',
  THINKING: 'Thinking...',
  SPEAKING: 'Interviewer speaking...',
  READY: 'Ready',
  LOADING: 'Loading...',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  SESSION_CREATE_FAILED: 'Failed to create session',
  SESSION_FETCH_FAILED: 'Failed to fetch session',
  SESSION_UPDATE_FAILED: 'Failed to update session',
  SESSION_NOT_FOUND: 'Session not found',
  SESSIONS_FETCH_FAILED: 'Failed to fetch sessions',
  DEEPGRAM_API_KEY_MISSING: 'Deepgram API key not configured',
  MONGODB_URI_MISSING: 'Please add your Mongo URI to .env.local',
  TTS_GENERATION_FAILED: 'TTS generation failed',
  STT_PROCESSING_FAILED: 'STT processing failed',
  AUDIO_STREAM_FAILED: 'No audio stream received',
  CONNECTION_TIMEOUT: 'Connection timeout',
  API_KEY_NOT_CONFIGURED: 'API key not configured',
  SPEECH_RECOGNITION_UNAVAILABLE: 'Speech recognition service is not available. Please try again later.',
  DEEPGRAM_CONNECTION_FAILED: 'Failed to connect to speech recognition service. Please check your API key and try again.',
} as const;

// Date and time formatting
export const DATE_FORMATS = {
  DATE: {
    year: 'numeric' as const,
    month: 'short' as const,
    day: 'numeric' as const,
  },
  TIME: {
    hour: '2-digit' as const,
    minute: '2-digit' as const,
  },
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Environment variables keys
export const ENV_KEYS = {
  DEEPGRAM_API_KEY: 'DEEPGRAM_API_KEY',
  DEEPGRAM_API_KEY_PUBLIC: 'NEXT_PUBLIC_DEEPGRAM_API_KEY',
  MONGODB_URI: 'MONGODB_URI',
  NODE_ENV: 'NODE_ENV',
} as const;
