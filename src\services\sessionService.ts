import clientPromise from '@/lib/mongodb';
import { InterviewSession, SessionStatus, Message } from '@/types/interview';
import { APP_CONFIG, ERROR_MESSAGES } from '@/constants/app';

export class SessionService {
  private static async getDatabase() {
    const client = await clientPromise;
    return client.db(APP_CONFIG.DATABASE_NAME);
  }

  /**
   * Creates a new interview session
   */
  static async createSession(): Promise<string> {
    try {
      const db = await this.getDatabase();
      const sessionId = crypto.randomUUID();
      
      const session: Omit<InterviewSession, '_id'> = {
        sessionId,
        messages: [],
        startTime: new Date(),
        status: SessionStatus.ACTIVE,
        audioRecorded: false,
      };
      
      await db.collection(APP_CONFIG.COLLECTIONS.SESSIONS).insertOne(session);
      return sessionId;
    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error(ERROR_MESSAGES.SESSION_CREATE_FAILED);
    }
  }

  /**
   * Retrieves all sessions, sorted by start time (newest first)
   */
  static async getAllSessions(): Promise<InterviewSession[]> {
    try {
      const db = await this.getDatabase();
      const sessions = await db
        .collection(APP_CONFIG.COLLECTIONS.SESSIONS)
        .find({})
        .sort({ startTime: -1 })
        .toArray();
      
      return sessions as InterviewSession[];
    } catch (error) {
      console.error('Error fetching sessions:', error);
      throw new Error(ERROR_MESSAGES.SESSIONS_FETCH_FAILED);
    }
  }

  /**
   * Retrieves a specific session by ID
   */
  static async getSessionById(sessionId: string): Promise<InterviewSession | null> {
    try {
      const db = await this.getDatabase();
      const session = await db
        .collection(APP_CONFIG.COLLECTIONS.SESSIONS)
        .findOne({ sessionId });
      
      return session as InterviewSession | null;
    } catch (error) {
      console.error('Error fetching session:', error);
      throw new Error(ERROR_MESSAGES.SESSION_FETCH_FAILED);
    }
  }

  /**
   * Updates a session with new data
   */
  static async updateSession(
    sessionId: string, 
    updates: Partial<InterviewSession>
  ): Promise<boolean> {
    try {
      const db = await this.getDatabase();
      const result = await db
        .collection(APP_CONFIG.COLLECTIONS.SESSIONS)
        .updateOne({ sessionId }, { $set: updates });
      
      return result.matchedCount > 0;
    } catch (error) {
      console.error('Error updating session:', error);
      throw new Error(ERROR_MESSAGES.SESSION_UPDATE_FAILED);
    }
  }

  /**
   * Ends an interview session
   */
  static async endSession(
    sessionId: string, 
    messages: Message[]
  ): Promise<boolean> {
    const updates = {
      messages,
      endTime: new Date(),
      status: SessionStatus.COMPLETED,
    };
    
    return this.updateSession(sessionId, updates);
  }

  /**
   * Calculates the duration of a session
   */
  static calculateDuration(startTime: Date, endTime?: Date): string {
    if (!endTime) return 'Ongoing';
    
    const duration = endTime.getTime() - startTime.getTime();
    const minutes = Math.floor(duration / 60000);
    return `${minutes} min`;
  }

  /**
   * Formats a date for display
   */
  static formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Formats a time for display
   */
  static formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }
}
