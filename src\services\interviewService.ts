import { Message, MessageRole, InterviewState } from '@/types/interview';
import { SYSTEM_PROMPTS, API_ENDPOINTS } from '@/constants/app';
import { AudioService } from './audioService';

export class InterviewService {
  /**
   * Gets the initial interviewer message
   */
  static getInitialMessage(): Message {
    return {
      id: '1',
      role: MessageRole.ASSISTANT,
      content: SYSTEM_PROMPTS.INITIAL_MESSAGE,
      timestamp: new Date(),
    };
  }

  /**
   * Sends a message to the chat API and returns the response
   */
  static async sendMessage(messages: Message[]): Promise<Response> {
    try {
      const response = await fetch(API_ENDPOINTS.CHAT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages }),
      });

      if (!response.ok) {
        throw new Error(`Chat API error: ${response.status}`);
      }

      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Creates a new session
   */
  static async createSession(): Promise<string> {
    try {
      const response = await fetch(API_ENDPOINTS.SESSIONS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Session creation failed: ${response.status}`);
      }

      const { sessionId } = await response.json();
      return sessionId;
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  }

  /**
   * Ends an interview session
   */
  static async endSession(sessionId: string, messages: Message[]): Promise<void> {
    try {
      const response = await fetch(`${API_ENDPOINTS.SESSIONS}/${sessionId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages,
          endTime: new Date(),
          status: 'completed',
        }),
      });

      if (!response.ok) {
        throw new Error(`Session end failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Error ending session:', error);
      throw error;
    }
  }

  /**
   * Speaks text using TTS and handles audio playback
   */
  static async speakText(
    text: string,
    onSpeakingStart?: () => void,
    onSpeakingEnd?: () => void
  ): Promise<void> {
    try {
      if (onSpeakingStart) {
        onSpeakingStart();
      }

      const audioBlob = await AudioService.generateSpeech(text);
      
      await AudioService.playAudio(audioBlob, () => {
        if (onSpeakingEnd) {
          onSpeakingEnd();
        }
      });
    } catch (error) {
      console.error('Error with TTS:', error);
      if (onSpeakingEnd) {
        onSpeakingEnd();
      }
      throw error;
    }
  }

  /**
   * Validates if a transcript is ready for submission
   */
  static isValidTranscript(transcript: string): boolean {
    const trimmed = transcript.trim();
    return trimmed.length > 0 && 
           trimmed !== 'Listening...' && 
           trimmed !== '';
  }

  /**
   * Creates a user message from transcript
   */
  static createUserMessage(transcript: string): Message {
    return {
      id: crypto.randomUUID(),
      role: MessageRole.USER,
      content: transcript.trim(),
      timestamp: new Date(),
    };
  }

  /**
   * Gets status text for display
   */
  static getStatusText(state: InterviewState): string {
    switch (state) {
      case InterviewState.LISTENING:
        return 'Listening...';
      case InterviewState.THINKING:
        return 'Thinking...';
      case InterviewState.SPEAKING:
        return 'Interviewer speaking...';
      case InterviewState.READY:
      default:
        return 'Ready';
    }
  }

  /**
   * Determines if user can interact based on current state
   */
  static canUserInteract(state: InterviewState): boolean {
    return state === InterviewState.READY || state === InterviewState.LISTENING;
  }

  /**
   * Determines if microphone button should be disabled
   */
  static isMicrophoneDisabled(state: InterviewState): boolean {
    return state === InterviewState.THINKING || state === InterviewState.SPEAKING;
  }

  /**
   * Gets the appropriate button variant based on listening state
   */
  static getMicrophoneButtonVariant(isListening: boolean): 'default' | 'destructive' {
    return isListening ? 'destructive' : 'default';
  }

  /**
   * Processes transcript updates (interim and final)
   */
  static processTranscriptUpdate(
    transcript: string,
    isFinal: boolean,
    currentTranscript: string,
    setInput: (value: string) => void,
    setCurrentTranscript: (value: string | ((prev: string) => string)) => void
  ): void {
    if (isFinal) {
      // Add final transcript to accumulated text
      setCurrentTranscript(prev => {
        const newTranscript = prev + transcript + ' ';
        console.log('Updated final transcript:', newTranscript);
        setInput(newTranscript);
        return newTranscript;
      });
    } else {
      // Show accumulated final + current interim
      setCurrentTranscript(prev => {
        const fullText = prev + transcript;
        setInput(fullText);
        return prev; // Don't update final transcript for interim results
      });
    }
  }

  /**
   * Handles the submission of a transcript after recording stops
   */
  static handleTranscriptSubmission(
    currentTranscript: string,
    setInput: (value: string) => void,
    handleSubmit: () => void,
    onReady: () => void,
    delay: number = 1000
  ): void {
    setTimeout(() => {
      const finalTranscript = currentTranscript.trim();
      console.log('Submitting transcript:', finalTranscript);
      
      if (this.isValidTranscript(finalTranscript)) {
        setInput(finalTranscript);
        setTimeout(() => {
          handleSubmit();
        }, 100);
      } else {
        console.log('No speech to submit, transcript:', finalTranscript);
        onReady();
      }
    }, delay);
  }
}
