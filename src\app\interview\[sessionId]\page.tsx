'use client';

import { useChat } from 'ai/react';
import { useState, useRef, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mi<PERSON>, MicOff, Square } from 'lucide-react';
import { createClient, LiveTranscriptionEvents } from '@deepgram/sdk';

export default function InterviewPage() {
  const params = useParams();
  const sessionId = params.sessionId as string;
  
  const { messages, input, setInput, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    initialMessages: [{
      id: '1',
      role: 'assistant',
      content: 'Hello! I\'m your AI interviewer today. I\'m excited to learn about your background and technical skills. Let\'s start with a brief introduction - could you tell me about yourself and what brings you to this interview?'
    }]
  });

  const [isListening, setIsListening] = useState(false);
  const [status, setStatus] = useState<'ready' | 'listening' | 'thinking' | 'speaking'>('ready');
  const deepgramConnectionRef = useRef<any>(null);
  const microphoneRef = useRef<MediaRecorder | null>(null);
  const [currentTranscript, setCurrentTranscript] = useState('');

  const initializeDeepgram = () => {
    return new Promise((resolve, reject) => {
      if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_DEEPGRAM_API_KEY) {
        console.log('Initializing Deepgram connection...');
        const deepgram = createClient(process.env.NEXT_PUBLIC_DEEPGRAM_API_KEY);
        
        const connection = deepgram.listen.live({
          model: 'nova-2',
          language: 'en-US',
          smart_format: true,
          interim_results: true,
          utterance_end_ms: 1000,
          vad_events: true,
        });

        connection.on(LiveTranscriptionEvents.Open, () => {
          console.log('Deepgram connection opened');
          deepgramConnectionRef.current = connection;
          resolve(connection);
        });

        connection.on(LiveTranscriptionEvents.Transcript, (data) => {
          const transcript = data.channel?.alternatives?.[0]?.transcript;
          if (transcript) {
            console.log('Deepgram transcript:', transcript, 'is_final:', data.is_final);
            
            if (data.is_final) {
              // Add final transcript to accumulated text
              setCurrentTranscript(prev => {
                const newTranscript = prev + transcript + ' ';
                console.log('Updated final transcript:', newTranscript);
                setInput(newTranscript);
                return newTranscript;
              });
            } else {
              // Show accumulated final + current interim
              setCurrentTranscript(prev => {
                const fullText = prev + transcript;
                setInput(fullText);
                return prev; // Don't update final transcript for interim results
              });
            }
          }
        });

        connection.on(LiveTranscriptionEvents.UtteranceEnd, () => {
          console.log('Utterance ended');
        });

        connection.on(LiveTranscriptionEvents.Close, () => {
          console.log('Deepgram connection closed');
          deepgramConnectionRef.current = null;
        });

        connection.on(LiveTranscriptionEvents.Error, (error) => {
          console.error('Deepgram error:', error);
          reject(error);
        });

        // Set a timeout to reject if connection doesn't open
        setTimeout(() => {
          if (!deepgramConnectionRef.current) {
            reject(new Error('Connection timeout'));
          }
        }, 5000);
      } else {
        reject(new Error('API key not configured'));
      }
    });
  };

  useEffect(() => {
    // Initialize on mount
    initializeDeepgram()
      .then(() => {
        console.log('Deepgram initialized successfully');
      })
      .catch((error) => {
        console.error('Failed to initialize Deepgram:', error);
      });

    return () => {
      if (deepgramConnectionRef.current) {
        deepgramConnectionRef.current.finish();
      }
    };
  }, []);

  const startListening = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Reset transcript for new session
      setCurrentTranscript('');
      setInput('');
      
      
      // Ensure Deepgram connection is ready
      if (!deepgramConnectionRef.current) {
        console.log('Reinitializing Deepgram connection...');
        try {
          await initializeDeepgram();
          startListeningWithConnection(stream);
        } catch (error) {
          console.error('Failed to initialize Deepgram connection:', error);
          alert('Failed to connect to speech recognition service. Please check your API key and try again.');
          setStatus('ready');
        }
      } else if (deepgramConnectionRef.current.getReadyState() === 1) {
        startListeningWithConnection(stream);
      } else {
        console.log('Deepgram connection state:', deepgramConnectionRef.current.getReadyState());
        console.log('Waiting for connection to open...');
        try {
          await initializeDeepgram();
          startListeningWithConnection(stream);
        } catch (error) {
          console.error('Failed to establish Deepgram connection:', error);
          alert('Speech recognition service is not available. Please try again later.');
          setStatus('ready');
        }
      }
      
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setStatus('ready');
    }
  };

  const startListeningWithConnection = (stream: MediaStream) => {
    const microphone = new MediaRecorder(stream, {
      mimeType: 'audio/webm'
    });
    
    microphone.ondataavailable = (event) => {
      if (event.data.size > 0 && deepgramConnectionRef.current && deepgramConnectionRef.current.getReadyState() === 1) {
        deepgramConnectionRef.current.send(event.data);
      }
    };
    
    microphone.start(100); // Send data every 100ms
    microphoneRef.current = microphone;
    
    setIsListening(true);
    setStatus('listening');
    setInput('Listening...');
  };

  const stopListening = () => {
    setIsListening(false);
    setStatus('thinking');
    
    // Stop microphone streaming to Deepgram
    if (microphoneRef.current) {
      microphoneRef.current.stop();
    }
    
    
    // Submit the current transcript after a short delay
    setTimeout(() => {
      const finalTranscript = currentTranscript.trim();
      console.log('Submitting transcript:', finalTranscript);
      
      if (finalTranscript && finalTranscript !== 'Listening...' && finalTranscript !== '') {
        setInput(finalTranscript);
        setTimeout(() => {
          handleSubmit();
        }, 100);
      } else {
        console.log('No speech to submit, transcript:', finalTranscript);
        setStatus('ready');
      }
    }, 1000); // Increased delay to allow final transcripts to come through
  };

  const speakText = async (text: string) => {
    try {
      setStatus('speaking');
      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });
      
      if (response.ok) {
        const audioBlob = await response.blob();
        const audio = new Audio();
        audio.src = URL.createObjectURL(audioBlob);
        
        // Use headphones/speakers output to minimize echo
        try {
          if ('setSinkId' in audio) {
            await (audio as any).setSinkId('default');
          }
        } catch (e) {
          console.log('Audio output selection not supported');
        }
        
        audio.onended = () => {
          setStatus('ready');
        };
        
        // Wait for audio to load before playing to prevent cutoff
        audio.oncanplaythrough = () => {
          audio.play();
        };
        
        // Fallback: play after a short delay if canplaythrough doesn't fire
        setTimeout(() => {
          if (audio.readyState >= 2) { // HAVE_CURRENT_DATA
            audio.play();
          }
        }, 100);
      } else {
        console.error('TTS API error:', await response.text());
        setStatus('ready');
      }
    } catch (error) {
      console.error('Error with TTS:', error);
      setStatus('ready');
    }
  };

  const [hasStarted, setHasStarted] = useState(false);

  const startInterview = () => {
    setHasStarted(true);
    const initialMessage = messages[0];
    if (initialMessage && initialMessage.role === 'assistant') {
      speakText(initialMessage.content);
    }
  };

  useEffect(() => {
    if (!hasStarted) return;
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && !isLoading) {
      // Skip the initial message since we handle it manually
      if (messages.length > 1) {
        speakText(lastMessage.content);
      }
    }
  }, [messages, isLoading, hasStarted]);

  const endInterview = async () => {
    try {
      await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages,
          endTime: new Date(),
          status: 'completed'
        })
      });
      window.location.href = `/history/${sessionId}`;
    } catch (error) {
      console.error('Failed to end interview:', error);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'listening': return 'Listening...';
      case 'thinking': return 'Thinking...';
      case 'speaking': return 'Interviewer speaking...';
      default: return 'Ready';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Interview Session</span>
              <div className="text-sm font-normal text-muted-foreground">
                Status: {getStatusText()}
              </div>
            </CardTitle>
          </CardHeader>
        </Card>

        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Conversation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {messages.map((message) => (
                <div key={message.id} className="p-3 rounded-lg bg-gray-100">
                  <div className="font-semibold mb-1">
                    {message.role === 'assistant' ? 'Interviewer' : 'You'}:
                  </div>
                  <div>{message.content}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-end mb-4">
              <Button variant="destructive" onClick={endInterview}>
                <Square className="w-4 h-4 mr-2" />
                End Interview
              </Button>
            </div>
            
            <div className="flex justify-center">
              {!hasStarted ? (
                <Button
                  size="lg"
                  onClick={startInterview}
                  className="h-12 px-6"
                >
                  Start Interview
                </Button>
              ) : (
                <Button
                  size="lg"
                  variant={isListening ? "destructive" : "default"}
                  onClick={isListening ? stopListening : startListening}
                  disabled={status === 'thinking' || status === 'speaking'}
                  className="h-16 w-16 rounded-full"
                >
                  {isListening ? (
                    <MicOff className="w-6 h-6" />
                  ) : (
                    <Mic className="w-6 h-6" />
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}