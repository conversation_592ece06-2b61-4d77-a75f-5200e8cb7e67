'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Mic, History } from 'lucide-react';
import { InterviewService } from '@/services/interviewService';
import { ErrorHand<PERSON> } from '@/utils/errorHandler';

export default function Home() {
  const router = useRouter();

  const startNewInterview = async () => {
    try {
      const sessionId = await InterviewService.createSession();
      router.push(`/interview/${sessionId}`);
    } catch (error) {
      const appError = ErrorHandler.handle(error, 'Home.startNewInterview');
      console.error('Failed to create session:', appError.message);
      alert(ErrorHandler.getUserMessage(error));
    }
  };

  const viewHistory = () => {
    router.push('/history');
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <CardTitle className='text-2xl font-bold'>
            AI Interview Practice
          </CardTitle>
          <CardDescription>
            Practice software engineering interviews with an AI interviewer
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <Button
            onClick={startNewInterview}
            size='lg'
            className='w-full h-12 text-base'
          >
            <Mic className='mr-2 h-5 w-5' />
            Start New Interview
          </Button>

          <Button
            onClick={viewHistory}
            variant='outline'
            size='lg'
            className='w-full h-12 text-base'
          >
            <History className='mr-2 h-5 w-5' />
            View Past Interviews
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
