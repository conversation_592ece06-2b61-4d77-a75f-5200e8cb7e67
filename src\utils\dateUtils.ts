import { DATE_FORMATS } from '@/constants/app';

/**
 * Utility functions for date and time formatting
 */
export class DateUtils {
  /**
   * Formats a date for display
   */
  static formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', DATE_FORMATS.DATE);
  }

  /**
   * Formats a time for display
   */
  static formatTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleTimeString('en-US', DATE_FORMATS.TIME);
  }

  /**
   * Calculates duration between two dates
   */
  static calculateDuration(startTime: Date | string, endTime?: Date | string): string {
    if (!endTime) return 'Ongoing';
    
    const start = typeof startTime === 'string' ? new Date(startTime) : startTime;
    const end = typeof endTime === 'string' ? new Date(endTime) : endTime;
    
    const duration = end.getTime() - start.getTime();
    const minutes = Math.floor(duration / 60000);
    return `${minutes} min`;
  }

  /**
   * Formats a date and time together
   */
  static formatDateTime(date: Date | string): string {
    return `${this.formatDate(date)} at ${this.formatTime(date)}`;
  }

  /**
   * Checks if a date is today
   */
  static isToday(date: Date | string): boolean {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    
    return dateObj.toDateString() === today.toDateString();
  }

  /**
   * Gets relative time (e.g., "2 hours ago")
   */
  static getRelativeTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    
    const diffMinutes = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return this.formatDate(dateObj);
  }

  /**
   * Validates if a date string is valid
   */
  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Converts date to ISO string safely
   */
  static toISOString(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toISOString();
  }
}
