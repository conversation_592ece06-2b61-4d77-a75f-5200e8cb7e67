import { ENV_KEYS, ERROR_MESSAGES } from '@/constants/app';

/**
 * Environment configuration management
 */
export class EnvironmentConfig {
  private static instance: EnvironmentConfig;
  private config: Map<string, string> = new Map();

  private constructor() {
    this.loadEnvironmentVariables();
  }

  /**
   * Gets the singleton instance
   */
  static getInstance(): EnvironmentConfig {
    if (!EnvironmentConfig.instance) {
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  /**
   * Loads environment variables
   */
  private loadEnvironmentVariables(): void {
    // Server-side environment variables
    if (typeof window === 'undefined') {
      this.config.set(ENV_KEYS.DEEPGRAM_API_KEY, process.env[ENV_KEYS.DEEPGRAM_API_KEY] || '');
      this.config.set(ENV_KEYS.MONGODB_URI, process.env[ENV_KEYS.MONGODB_URI] || '');
      this.config.set(ENV_KEYS.NODE_ENV, process.env[ENV_KEYS.NODE_ENV] || 'development');
    }

    // Client-side environment variables
    this.config.set(
      ENV_KEYS.DEEPGRAM_API_KEY_PUBLIC, 
      process.env[ENV_KEYS.DEEPGRAM_API_KEY_PUBLIC] || ''
    );
  }

  /**
   * Gets an environment variable value
   */
  get(key: string): string {
    return this.config.get(key) || '';
  }

  /**
   * Checks if an environment variable exists and is not empty
   */
  has(key: string): boolean {
    const value = this.config.get(key);
    return value !== undefined && value !== '';
  }

  /**
   * Gets a required environment variable, throws if missing
   */
  getRequired(key: string): string {
    const value = this.get(key);
    if (!value) {
      throw new Error(`Required environment variable ${key} is missing`);
    }
    return value;
  }

  /**
   * Validates all required environment variables
   */
  validateRequired(): { isValid: boolean; missingKeys: string[] } {
    const requiredKeys = [ENV_KEYS.MONGODB_URI];
    const missingKeys: string[] = [];

    for (const key of requiredKeys) {
      if (!this.has(key)) {
        missingKeys.push(key);
      }
    }

    return {
      isValid: missingKeys.length === 0,
      missingKeys,
    };
  }

  /**
   * Gets MongoDB URI with validation
   */
  getMongoDBUri(): string {
    const uri = this.get(ENV_KEYS.MONGODB_URI);
    if (!uri) {
      throw new Error(ERROR_MESSAGES.MONGODB_URI_MISSING);
    }
    return uri;
  }

  /**
   * Gets Deepgram API key for server-side use
   */
  getDeepgramApiKey(): string {
    const key = this.get(ENV_KEYS.DEEPGRAM_API_KEY);
    if (!key) {
      throw new Error(ERROR_MESSAGES.DEEPGRAM_API_KEY_MISSING);
    }
    return key;
  }

  /**
   * Gets Deepgram API key for client-side use
   */
  getDeepgramApiKeyPublic(): string {
    const key = this.get(ENV_KEYS.DEEPGRAM_API_KEY_PUBLIC);
    if (!key) {
      throw new Error(ERROR_MESSAGES.API_KEY_NOT_CONFIGURED);
    }
    return key;
  }

  /**
   * Checks if running in development mode
   */
  isDevelopment(): boolean {
    return this.get(ENV_KEYS.NODE_ENV) === 'development';
  }

  /**
   * Checks if running in production mode
   */
  isProduction(): boolean {
    return this.get(ENV_KEYS.NODE_ENV) === 'production';
  }

  /**
   * Gets all configuration as an object (for debugging)
   */
  getAll(): Record<string, string> {
    const result: Record<string, string> = {};
    for (const [key, value] of this.config.entries()) {
      // Don't expose sensitive values in full
      if (key.includes('API_KEY') || key.includes('URI')) {
        result[key] = value ? '***' : '';
      } else {
        result[key] = value;
      }
    }
    return result;
  }

  /**
   * Reloads environment variables (useful for testing)
   */
  reload(): void {
    this.config.clear();
    this.loadEnvironmentVariables();
  }
}

// Export singleton instance
export const envConfig = EnvironmentConfig.getInstance();
