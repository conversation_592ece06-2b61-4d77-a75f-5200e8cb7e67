import { google } from '@ai-sdk/google';
import { convertToCoreMessages, streamText } from 'ai';
import { NextRequest } from 'next/server';
import { SYSTEM_PROMPTS } from '@/constants/app';

export class ChatController {
  /**
   * <PERSON>les chat message processing and streaming response
   */
  static async processMessage(req: NextRequest) {
    try {
      const { messages } = await req.json();

      const result = await streamText({
        model: google('gemini-2.0-flash-exp'),
        system: SYSTEM_PROMPTS.INTERVIEWER,
        messages: convertToCoreMessages(messages),
      });

      return result.toDataStreamResponse();
    } catch (error) {
      console.error('Chat processing error:', error);
      throw error;
    }
  }

  /**
   * Validates incoming chat request
   */
  static validateChatRequest(body: any): boolean {
    return body && Array.isArray(body.messages) && body.messages.length > 0;
  }

  /**
   * Sanitizes messages before processing
   */
  static sanitizeMessages(messages: any[]): any[] {
    return messages.map(message => ({
      id: message.id || crypto.randomUUID(),
      role: message.role,
      content: message.content?.trim() || '',
      timestamp: message.timestamp || new Date(),
    }));
  }
}
