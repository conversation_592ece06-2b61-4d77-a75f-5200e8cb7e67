/**
 * Utility functions for data validation
 */
export class ValidationUtils {
  /**
   * Validates if a string is not empty after trimming
   */
  static isNonEmptyString(value: any): value is string {
    return typeof value === 'string' && value.trim().length > 0;
  }

  /**
   * Validates if a value is a valid UUID
   */
  static isValidUUID(value: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  }

  /**
   * Validates if a transcript is ready for submission
   */
  static isValidTranscript(transcript: string): boolean {
    const trimmed = transcript.trim();
    return trimmed.length > 0 && 
           trimmed !== 'Listening...' && 
           trimmed !== '';
  }

  /**
   * Validates email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validates if a number is within a range
   */
  static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * Validates if an object has required properties
   */
  static hasRequiredProperties<T extends Record<string, any>>(
    obj: any,
    requiredProps: (keyof T)[]
  ): obj is T {
    if (!obj || typeof obj !== 'object') return false;
    
    return requiredProps.every(prop => 
      obj.hasOwnProperty(prop) && obj[prop] !== undefined && obj[prop] !== null
    );
  }

  /**
   * Validates if a string meets minimum length requirement
   */
  static meetsMinLength(value: string, minLength: number): boolean {
    return value.trim().length >= minLength;
  }

  /**
   * Validates if a string doesn't exceed maximum length
   */
  static withinMaxLength(value: string, maxLength: number): boolean {
    return value.length <= maxLength;
  }

  /**
   * Sanitizes a string by removing potentially harmful characters
   */
  static sanitizeString(value: string): string {
    return value
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/[^\w\s.,!?;:'"()-]/g, ''); // Keep only safe characters
  }

  /**
   * Validates if a value is a positive number
   */
  static isPositiveNumber(value: any): value is number {
    return typeof value === 'number' && value > 0 && !isNaN(value);
  }

  /**
   * Validates if a value is a non-negative number
   */
  static isNonNegativeNumber(value: any): value is number {
    return typeof value === 'number' && value >= 0 && !isNaN(value);
  }

  /**
   * Validates if an array is not empty
   */
  static isNonEmptyArray<T>(value: any): value is T[] {
    return Array.isArray(value) && value.length > 0;
  }

  /**
   * Validates if a value is a valid JSON string
   */
  static isValidJSON(value: string): boolean {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validates if a URL is valid
   */
  static isValidURL(value: string): boolean {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validates session status
   */
  static isValidSessionStatus(status: string): boolean {
    return ['active', 'completed'].includes(status);
  }

  /**
   * Validates message role
   */
  static isValidMessageRole(role: string): boolean {
    return ['user', 'assistant'].includes(role);
  }
}
